<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\RewardCategoryResource\Pages;

use <PERSON>chenorg\BaseFilamentPlugin\Filament\Resources\RewardCategoryResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitEditRecord;

class EditRewardCategory extends EditRecord
{
    use CCTraitEditRecord;

    protected static string $resource = RewardCategoryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
