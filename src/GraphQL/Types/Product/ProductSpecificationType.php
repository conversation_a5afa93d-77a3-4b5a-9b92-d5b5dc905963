<?php

namespace Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\Product;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasTranslation;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductSpecification;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Type as GraphQLType;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceProductSpecification;

class ProductSpecificationType extends GraphQLType
{

    use HasTranslation;


    protected $attributes = [
        'name'        => 'ProductSpecification',
        'description' => 'A type that represents a product specification',
        'model'       => ProductSpecification::class,
    ];
    protected ServiceProductSpecification $serviceProductSpecification;

    public function __construct(ServiceProductSpecification $serviceProductSpecification)
    {
        $this->serviceProductSpecification = $serviceProductSpecification;
    }

    public function fields(): array
    {
        $fields = [
            'id'              => [
                'type'        => Type::nonNull(Type::int()),
                'description' => 'The ID of the product specification',
            ],
            'combination_key' => [
                'type'        => Type::string(),
                'description' => 'The combination_key of the product',
            ],
            'combination_name' => [
                'type'        => Type::string(),
                'description' => 'The combination_name of the product',
                'resolve' => function ($root) {
                    $lang = app()->getLocale();
                    $serviceProductSpecification = app(ServiceProductSpecification::class);
                    return $serviceProductSpecification->getCombinationName($root->combination_key, $lang);
                },
            ],

            'listing_price'   => [
                'type'        => Type::float(),
                'description' => 'The listing price of the product',
                'resolve' => function ($root) {
                    return $this->serviceProductSpecification->getListingPrice($root);
                },
            ],
            'selling_price'   => [
                'type'        => Type::float(),
                'description' => 'The selling price of the product',
                'resolve' => function ($root) {
                    return $this->serviceProductSpecification->getSellingPrice($root);
                },
            ],
            'inventory'       => [
                'type'        => Type::int(),
                'description' => 'The available inventory of the product',
                'resolve' => function ($root) {
                    return $root->inventory - ($root->reservingItem->quantity ?? 0);
                },
            ],
            'type'            => [
                'type'        => Type::string(),
                'description' => 'The type of the product specification',
            ],
            'sku'             => [
                'type'        => Type::string(),
                'description' => 'The SKU of the product',
            ],
            'status'          => [
                'type'        => Type::int(),
                'description' => 'The status of the product specification (0 for closed, 1 for open)',
            ],
            'sort'            => [
                'type'        => Type::int(),
                'description' => 'The sort order of the specification',
            ],
            'created_at'      => [
                'type'        => Type::string(),
                'description' => 'The creation timestamp of the product specification',
            ],
            'updated_at'      => [
                'type'        => Type::string(),
                'description' => 'The last update timestamp of the product specification',
            ],
        ];

        return array_merge($this->getTranslatableFields(new ProductSpecificationTranslationType()), $fields);


    }
}
