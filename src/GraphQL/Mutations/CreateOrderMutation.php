<?php

namespace <PERSON>g\BaseFilamentPlugin\GraphQL\Mutations;

use Exception;
use GraphQL\Type\Definition\Type;
use Illuminate\Validation\Rule;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Mutation;
use Stephen<PERSON>org\BaseFilamentPlugin\Contracts\OrderServiceInterface;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumInvoiceMethod;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumPaymentMethod;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumShippingMethod;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use Stephenchenorg\BaseFilamentPlugin\Models\Order;

class CreateOrderMutation extends Mutation
{
    protected $attributes = [
        'name' => EnumNames::CreateOrder,
        'description' => 'Create a new order and return order',
    ];

    /**
     * @var OrderServiceInterface
     */
    private OrderServiceInterface $serviceOrder;

    /**
     * @param OrderServiceInterface $serviceOrder
     */
    public function __construct(OrderServiceInterface $serviceOrder)
    {
        $this->serviceOrder = $serviceOrder;
    }

    public function type(): Type
    {
        return GraphQL::type(EnumNames::Order);
    }

    public function args(): array
    {
        return [

            // 購物車項目
            'items' => [
                'name' => 'items',
                'type' => Type::listOf(GraphQL::type('OrderItemInput')),
                'rules' => ['required', 'array', 'min:1', 'max:15'],
            ],

            // 點數折抵
            'redeem_points' => [
                'name' => 'redeem_points',
                'type' => Type::int(),
                'rules' => ['nullable', 'integer', 'min:0'],
            ],

            // 基本資訊
            'name' => [
                'name' => 'name',
                'type' => Type::string(),
                'rules' => ['required', 'string', 'max:255'],
            ],
            'phone' => [
                'name' => 'phone',
                'type' => Type::string(),
                'rules' => ['required', 'string', 'max:20'],
            ],
            'email' => [
                'name' => 'email',
                'type' => Type::string(),
                'rules' => ['required', 'email', 'max:255'],
            ],

            // 付款方式 (必填)
            'payment_method' => [
                'name' => 'payment_method',
                'type' => Type::string(),
                'rules' => [
                    'required',
                    Rule::in(EnumPaymentMethod::getAvailableMethodValues())
                ],
            ],

            // 運送方式 (必填)
            'shipping_method' => [
                'name' => 'shipping_method',
                'type' => Type::string(),
                'rules' => [
                    'required',
                    Rule::in(EnumShippingMethod::getAvailableMethodValues())
                ],
            ],

            // 地址資訊 (二選一：store_address_id 或詳細地址)
            'store_address_id' => [
                'name' => 'store_address_id',
                'type' => Type::int(),
                'rules' => ['nullable', 'integer', 'exists:store_addresses,id'],
            ],
            'country_code' => [
                'name' => 'country_code',
                'type' => Type::string(),
                'rules' => ['nullable', 'string', 'max:10'],
            ],
            'state' => [
                'name' => 'state',
                'type' => Type::string(),
                'rules' => ['nullable', 'string', 'max:100'],
            ],
            'city' => [
                'name' => 'city',
                'type' => Type::string(),
                'rules' => ['nullable', 'string', 'max:100'],
            ],
            'district' => [
                'name' => 'district',
                'type' => Type::string(),
                'rules' => ['nullable', 'string', 'max:100'],
            ],
            'postal_code' => [
                'name' => 'postal_code',
                'type' => Type::string(),
                'rules' => ['required_without:store_address_id', 'string', 'max:10'],
            ],
            'address_line1' => [
                'name' => 'address_line1',
                'type' => Type::string(),
                'rules' => ['required_without:store_address_id', 'string', 'max:255'],
            ],
            'address_line2' => [
                'name' => 'address_line2',
                'type' => Type::string(),
                'rules' => ['nullable', 'string', 'max:255'],
            ],

            // 發票資訊
            'invoice_method' => [
                'name' => 'invoice_method',
                'type' => Type::string(),
                'rules' => [
                    'required',
                    Rule::in(EnumInvoiceMethod::getAvailableMethodValues())
                ],
            ],

            // 載具相關欄位 (當 invoice_method 為載具類型時必填)
            'carrier_value' => [
                'name' => 'carrier_value',
                'type' => Type::string(),
                'rules' => [
                    'required_if:invoice_method,' . EnumInvoiceMethod::MOBILE_BARCODE->value . ',' . EnumInvoiceMethod::CITIZEN_CARD->value,
                    'max:50'
                ],
            ],

            // 二聯/三聯發票相關欄位 (當 invoice_method 為 duplicate 或 triplicate 時必填)
            'invoice_address' => [
                'name' => 'invoice_address',
                'type' => Type::string(),
                'rules' => [
                    'required_if:invoice_method,' . EnumInvoiceMethod::DUPLICATE->value . ',' . EnumInvoiceMethod::TRIPLICATE->value,
                    'max:255'
                ],
            ],
            'vat' => [
                'name' => 'vat',
                'type' => Type::string(),
                'rules' => [
                    'required_if:invoice_method,' . EnumInvoiceMethod::TRIPLICATE->value,
                    'max:20'
                ],
            ],
            'invoice_title' => [
                'name' => 'invoice_title',
                'type' => Type::string(),
                'rules' => [
                    'required_if:invoice_method,' . EnumInvoiceMethod::TRIPLICATE->value,
                    'max:255'
                ],
            ],

            // 捐贈相關欄位 (當 invoice_method 為 donation 時必填)
            'love_code' => [
                'name' => 'love_code',
                'type' => Type::string(),
                'rules' => [
                    'required_if:invoice_method,' . EnumInvoiceMethod::DONATION->value,
                    'max:20'
                ],
            ],
        ];
    }

    /**
     * @throws Exception
     */
    public function resolve($root, $args): Order
    {
        $order = $this->serviceOrder->createOrder($args);
        return $order->load('items');
    }
}
