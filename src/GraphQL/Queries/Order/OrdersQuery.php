<?php

namespace <PERSON>org\BaseFilamentPlugin\GraphQL\Queries\Order;

use GraphQL\Type\Definition\Type;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Query;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasPagination;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasSort;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasTranslation;
use Stephen<PERSON>org\BaseFilamentPlugin\Models\Order;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceOrder;

class OrdersQuery extends Query
{
    use HasTranslation;
    use HasPagination, HasSort;

    protected $attributes = [
        'name' => EnumNames::Orders,
        'description' => 'Get a paginated list of orders with optional date filtering',
    ];

    public ServiceOrder $serviceOrder;

    public function __construct(ServiceOrder $serviceOrder)
    {
        $this->serviceOrder = $serviceOrder;
    }

    public function type(): Type
    {
        return GraphQL::paginate(EnumNames::Order);
    }

    public function args(): array
    {
        return [
            'start_date' => [
                'name' => 'start_date',
                'type' => Type::string(),
                'description' => 'Filter orders created after this date (Y-m-d H:i:s format)',
            ],
            'end_date' => [
                'name' => 'end_date',
                'type' => Type::string(),
                'description' => 'Filter orders created before this date (Y-m-d H:i:s format)',
            ],
            ...$this->getPaginationArgs(),
            ...$this->getSortArgs(),
        ];
    }

    /**
     * @param $root
     * @param $args
     * @return LengthAwarePaginator
     */
    public function resolve($root, $args): LengthAwarePaginator
    {
        $query = Order::query()->visibleTo();

        if (!empty($args['start_date'])) {
            $query = $query->where('created_at', '>=', $args['start_date']);
        }

        if (!empty($args['end_date'])) {
            $query = $query->where('created_at', '<=', $args['end_date']);
        }

        $query = $this->sortQuery($query, $args['sort_column'], $args['sort_by']);

        return $this->paginateQuery($query, $args['per_page'], $args['page']);
    }
}
