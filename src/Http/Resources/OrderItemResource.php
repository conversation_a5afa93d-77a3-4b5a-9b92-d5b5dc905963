<?php

namespace Stephenchenorg\BaseFilamentPlugin\Http\Resources;

use GraphQL\Type\Definition\Type;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceProductSpecification;
use Stephenchenorg\CsCoreFilamentPlugin\Services\CCServiceImage;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Response\CCTraitResponseImage;

class OrderItemResource extends JsonResource
{
    use CCTraitResponseImage;

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $lang = app()->getLocale() ?? ServiceLanguage::getDefaultLanguage();
        $image =  $this->specification->product?->images->first()->image ?? null;

        return [
            'id' => $this->id,
            'order_id' => $this->order_id,
            'product_specification_id' => $this->product_specification_id,
            'image'=> self::toFormatImage($image),
            'title' => $this->title,
            'quantity' => $this->quantity,
            'unit_price' => (int)$this->unit_price,
            'total_amount' => (int)$this->total_amount,
        ];
    }
}
