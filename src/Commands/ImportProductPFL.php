<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin\Commands;

use Stephen<PERSON>org\BaseFilamentPlugin\Enum\EnumProductDetailType;
use Stephen<PERSON>org\BaseFilamentPlugin\Enum\EnumProductType;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumStatus;
use Stephenchenorg\BaseFilamentPlugin\Models\Product;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductCategory;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductSpecification;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;


class ImportProductPFL extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'base:import-product-pfl';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $path = database_path("seeders/prod/pfl.csv");

        if (!file_exists($path)) {
            $this->error("CSV 檔案不存在: {$path}");
        }

        $imageJsonPath = database_path('seeders/prod/images_structure.json');
        $imageJsonContent = File::get($imageJsonPath);
        $imageJsonContent = json_decode($imageJsonContent, true);

        $handle = fopen($path, 'r');
        $header = fgetcsv($handle);

        $data = [];
        while (($row = fgetcsv($handle)) !== false) {
            $row = array_combine($header, $row);
            if ($row['停止上架'] == 0) continue;
            $category = $this->createCategory($row);
            $images = $imageJsonContent[$row['產品代號']]['files'] ?? [];
            $product = $this->createProduct($row, $category,$images);
            $spec = $this->createProductSpecification($row, $product);
        }

    }

    public function createCategory(array $data): ProductCategory
    {

        $lv1 = ProductCategory::query()->firstOrCreate([
            'code' => $data['大類類別代號'],
        ], [
            'code' => $data['大類類別代號'],
            'type' => EnumProductType::Product,
            'is_hottest' => false,
            'status' => EnumStatus::ENABLE->value,
        ]);

        $lv1->translations()->firstOrCreate([
            'product_category_id' => $lv1->id,
            'title' => $data['大類類別名稱'],
        ], [
            'product_category_id' => $lv1->id,
            'title' => $data['大類類別名稱'],
            'lang' => 'zh_TW',
        ]);


        if (empty($data['中類類別代號'])) {
            return $lv1;
        }

        $lv2 = ProductCategory::query()->firstOrCreate([
            'code' => $data['中類類別代號'],
            'parent_id' => $lv1->id,
        ], [
            'type' => EnumProductType::Product,
            'is_hottest' => false,
            'status' => EnumStatus::ENABLE->value,
        ]);

        $lv2->translations()->firstOrCreate([
            'product_category_id' => $lv2->id,
            'title' => $data['中類類別名稱'],
        ], [
            'product_category_id' => $lv2->id,
            'title' => $data['中類類別名稱'],
            'lang' => 'zh_TW',
        ]);

        if (empty($data['小類類別代號'])) {
            return $lv2;
        }

        $lv3 = ProductCategory::query()->firstOrCreate([
            'code' => $data['小類類別代號'],
            'parent_id' => $lv2->id,
        ], [
            'type' => EnumProductType::Product,
            'is_hottest' => false,
            'status' => EnumStatus::ENABLE->value,
        ]);

        $lv3->translations()->firstOrCreate([
            'product_category_id' => $lv3->id,
            'title' => $data['小類類別名稱'],
        ], [
            'product_category_id' => $lv3->id,
            'title' => $data['小類類別名稱'],
            'lang' => 'zh_TW',
        ]);

        if (empty($data['細類類別一代號'])) {
            return $lv3;
        }

        $lv4 = ProductCategory::query()->firstOrCreate([
            'code' => $data['細類類別一代號'],
            'parent_id' => $lv3->id,
        ], [
            'type' => EnumProductType::Product,
            'is_hottest' => false,
            'status' => EnumStatus::ENABLE->value,
        ]);

        $lv4->translations()->firstOrCreate([
            'product_category_id' => $lv4->id,
            'title' => $data['細類類別一名稱'],
        ], [
            'product_category_id' => $lv4->id,
            'title' => $data['細類類別一名稱'],
            'lang' => 'zh_TW',
        ]);

        if (empty($data['細項類別二代號'])) {
            return $lv4;
        }

        $lv5 = ProductCategory::query()->firstOrCreate([
            'code' => $data['細項類別二代號'],
            'parent_id' => $lv4->id,
        ], [
            'type' => EnumProductType::Product,
            'is_hottest' => false,
            'status' => EnumStatus::ENABLE->value,
        ]);

        $lv5->translations()->firstOrCreate([
            'product_category_id' => $lv5->id,
            'title' => $data['細項類別二名稱'],
        ], [
            'product_category_id' => $lv5->id,
            'title' => $data['細項類別二名稱'],
            'lang' => 'zh_TW',
        ]);

        return $lv5;
    }

    public function createProduct(array $data, ProductCategory $category,array $images): Product
    {
        $product = Product::query()->firstOrCreate([
            'part_number' => $data['產品代號'],
            'product_category_id' => $category->id,
        ], [
            'type' => EnumProductType::Product->value,
            'is_hottest' => false,
            'is_newest' => false,
            'status' => EnumStatus::ENABLE->value,
        ]);


        $product->translations()->create([
            'title' => $data['產品名稱'],
            'content_1' => $data['簡要說明1'],
            'content_2' => $data['簡要說明2'],
            'content_3' => $data['簡要說明3'],
            'lang' => 'zh_TW',
            'product_id' => $product->id,
        ]);


        $detail = $product->details()->create([
            'product_id' => $product->id,
            'type' => EnumProductDetailType::DESCRIPTION->value,
        ]);

        $text = "產品代號 : {$data['產品代號']}\n" .
            "產品條碼 : {$data['CODE128']}\n" .
            "產品名稱 : {$data['產品名稱']}\n" .
            "產品類別 : {$category->translations->where('lang', 'zh_TW')->first()->title}\n" .
            "耐熱溫度 : {$data['耐熱溫度']}\n" .
            "材質 : {$data['材質']}\n" .
            "款式 : {$data['顏色(規格1)']}\n" .
            "尺寸 : {$data['尺寸(規格2)']}\n" .
            "規格 : {$data['包裝方式']}\n" .
            "產地 : {$data['產地國別']}\n" .
            "建議售價 : {$data['建議售價']}\n" .
            "{$data['簡要說明3']}";


        $detail->translations()->create([
            'content' => $text,
            'lang' => 'zh_TW',
        ]);


        foreach ($images as $image) {
            $product->images()->create([
                'is_default' => false,
                'image' => "image/{$data['產品代號']}/{$image}",
                'image_mobile'=> "image/{$data['產品代號']}/compressed_{$image}",
            ]);
        }


        return $product;
    }

    public function createProductSpecification(array $data, Product $product): ProductSpecification
    {
        $spec = ProductSpecification::query()->create([
            'listing_price' => $data['建議售價'],
            'selling_price' => $data['銷售單價1'],
            'inventory' => 0,
            'sku' => $data['產品代號'],
            'product_id' => $product->id,
            'status' => EnumStatus::ENABLE->value,
        ]);


        $spec->translations()->create([
            'title' => $data['產品名稱'],
            'lang' => 'zh_TW',
        ]);

        return $spec;
    }
}
