<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin\Contracts;

use Exception;
use Illuminate\Validation\ValidationException;
use Stephenchenorg\BaseFilamentPlugin\Models\Order;

/**
 * 訂單服務介面
 */
interface OrderServiceInterface
{
    /**
     * 建立訂單
     *
     * @param array $data 包含完整的結帳資料
     * @return Order 新建立的訂單物件
     * @throws Exception 如果建立訂單失敗，會拋出異常
     */
    public function createOrder(array $data): Order;

    /**
     * 計算訂單總金額
     *
     * @param array $items
     * @param string $shippingMethod
     * @param string $invoiceMethod
     * @param int $redeemPoints
     * @return array
     * @throws ValidationException
     */
    public function calculateOrderAmounts(array $items, string $shippingMethod, string $invoiceMethod, int $redeemPoints = 0): array;

    /**
     * 檢查訂單用到的庫存量是否足夠
     *
     * @param array $items
     * @return void
     * @throws ValidationException
     */
    public function checkInventory(array $items): void;

    /**
     * 獲得訂單可用的物流方式以及對應運費
     *
     * @param array $items
     * @return array
     */
    public function getAvailableShippingMethodAndCost(array $items): array;

    /**
     * 獲得該筆訂單最大可抵免的紅利點數
     *
     * @param array $items
     * @return int
     */
    public function getMaxRedeemablePoints(array $items): int;
}
