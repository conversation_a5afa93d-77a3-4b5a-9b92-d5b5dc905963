<?php

namespace <PERSON><PERSON>org\BaseFilamentPlugin\Contracts;

use Exception;
use Illuminate\Validation\ValidationException;
use <PERSON><PERSON>org\BaseFilamentPlugin\DTO\Order\CalculateOrderAmountsDTO;
use <PERSON><PERSON>org\BaseFilamentPlugin\DTO\Order\CreateOrderDTO;
use Stephenchenorg\BaseFilamentPlugin\DTO\Order\OrderItemDTO;
use Stephenchenorg\BaseFilamentPlugin\DTO\Order\OrderItemListDTO;
use Stephen<PERSON>org\BaseFilamentPlugin\Models\Order;

/**
 * 訂單服務介面
 */
interface OrderServiceInterface
{
    /**
     * 建立訂單
     *
     * @param CreateOrderDTO $data 包含完整的結帳資料
     * @return Order 新建立的訂單物件
     * @throws Exception 如果建立訂單失敗，會拋出異常
     */
    public function createOrder(CreateOrderDTO $data): Order;

    /**
     * 計算訂單總金額
     *
     * @param CalculateOrderAmountsDTO $data
     * @return array
     * @throws ValidationException
     */
    public function calculateOrderAmounts(CalculateOrderAmountsDTO $data): array;

    /**
     * 檢查訂單用到的庫存量是否足夠
     *
     * @param OrderItemListDTO $items
     * @return void
     * @throws ValidationException
     */
    public function checkInventory(OrderItemListDTO $items): void;

    /**
     * 獲得訂單可用的物流方式以及對應運費
     *
     * @param OrderItemListDTO $items
     * @return array
     */
    public function getAvailableShippingMethodAndCost(OrderItemListDTO $items): array;

    /**
     * 獲得該筆訂單最大可抵免的紅利點數
     *
     * @param OrderItemListDTO $items
     * @return int
     */
    public function getMaxRedeemablePoints(OrderItemListDTO $items): int;
}
