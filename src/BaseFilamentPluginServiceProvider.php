<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin;

use Filament\Support\Assets\Asset;
use Filament\Support\Facades\FilamentAsset;
use Filament\Support\Facades\FilamentIcon;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Filesystem\Filesystem;
use Livewire\Features\SupportTesting\Testable;
use Spatie\LaravelPackageTools\Commands\InstallCommand;
use Spatie\LaravelPackageTools\Package;
use Spatie\LaravelPackageTools\PackageServiceProvider;
use Stephenchenorg\BaseFilamentPlugin\Commands\BaseFilamentPluginCommand;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceRegister;
use Stephenchenorg\BaseFilamentPlugin\Testing\TestsBaseFilamentPlugin;
use TsaiYiHua\ECPay\ECPay;

class BaseFilamentPluginServiceProvider extends PackageServiceProvider
{
    public static string $name = 'base-filament-plugin';

    public static string $viewNamespace = 'base-filament-plugin';

    public function configurePackage(Package $package): void
    {
        /*
         * This class is a Package Service Provider
         *
         * More info: https://github.com/spatie/laravel-package-tools
         */
        $package->name(static::$name)
            ->hasCommands($this->getCommands())
            ->hasInstallCommand(function (InstallCommand $command) {
                $command
                    ->publishConfigFile()
                    ->publishMigrations()
                    ->askToRunMigrations()
                    ->askToStarRepoOnGitHub('stephenchenorg/base-filament-plugin');
            });

        $configFileName = $package->shortName();

        if (file_exists($package->basePath("/../config"))) {
            $package->hasConfigFile($this->getConfigs());
        }

        if (file_exists($package->basePath('/../database/migrations'))) {
            $package->hasMigrations($this->getMigrations());
        }

        if (file_exists($package->basePath('/../resources/lang'))) {
            $package->hasTranslations();
        }

        if (file_exists($package->basePath('/../resources/views'))) {
            $package->hasViews(static::$viewNamespace);
        }


        ServiceRegister::registerServices($this->app);

        // 覆蓋掉原本的 routes
        ECPay::ignoreRoutes();
    }

    public function packageRegistered(): void {

    }

    public function packageBooted(): void
    {
        // Asset Registration
        FilamentAsset::register(
            $this->getAssets(),
            $this->getAssetPackageName()
        );

        FilamentAsset::registerScriptData(
            $this->getScriptData(),
            $this->getAssetPackageName()
        );

        // Icon Registration
        FilamentIcon::register($this->getIcons());

        // Handle Stubs
        if (app()->runningInConsole()) {


            foreach (app(Filesystem::class)->files(__DIR__ . '/../stubs/') as $file) {
                $this->publishes([
                    $file->getRealPath() => base_path("stubs/base-filament-plugin/{$file->getFilename()}"),
                ], 'base-filament-plugin-stubs');
            }

            ServiceRegister::registerBaseSeederAfterMigration();
        }
        else
        {
            ServiceRegister::registerNavigationItems();
            ServiceRegister::macroFilamentColumn();

            ServiceRegister::registerObservers();
            ServiceRegister::registerListeners();
            ServiceRegister::registerPolicies();
            ServiceRegister::registerLivewireComponents();
        }


        ServiceRegister::registerConfigs($this);
        ServiceRegister::registerRoutes($this);

        // Testing
        Testable::mixin(new TestsBaseFilamentPlugin);

        $this->app->booted(function () {
            $schedule = $this->app->make(Schedule::class);
            $schedule->command('base:check-orders')->daily(); // 每日執行
        });
    }

    protected function getAssetPackageName(): ?string
    {
        return 'stephenchenorg/base-filament-plugin';
    }

    /**
     * @return array<Asset>
     */
    protected function getAssets(): array
    {
        return [
            // AlpineComponent::make('base-filament-plugin', __DIR__ . '/../resources/dist/components/base-filament-plugin.js'),
//            Css::make('base-filament-plugin-styles', __DIR__ . '/../resources/dist/base-filament-plugin.css'),
//            Js::make('base-filament-plugin-scripts', __DIR__ . '/../resources/dist/base-filament-plugin.js'),
        ];
    }

    /**
     * @return array<class-string>
     */
    protected function getCommands(): array
    {
        return [
            BaseFilamentPluginCommand::class,
            Commands\CheckCountCommand::class,
            Commands\CheckDatabaseSchema::class,
            Commands\CheckOrdersCommand::class,
            Commands\CheckProductCategoryTreeEnabledCommand::class,
            Commands\CustomScribeGenerate::class,
            Commands\ExportColumnsToCSV::class,
            Commands\ImportProductPFL::class,
            Commands\ListImages::class,
            Commands\MigrateMarkAllAsExecuted::class,
            Commands\NullifyAdminIds::class,
            Commands\Playground::class,
            Commands\Playground2::class,
            Commands\RearrangeColumns::class,
            Commands\ResetPermissionsCommand::class,
            Commands\ScanMigrations::class,
            Commands\SeedAdminCommand::class,
            Commands\UpdateColumnLengths::class,
            Commands\UpdateSeoFields::class,
        ];
    }

    /**
     * @return array<string>
     */
    protected function getIcons(): array
    {
        return [];
    }

    /**
     * @return array<string>
     */
    protected function getRoutes(): array
    {
        return [];
    }

    /**
     * @return array<string, mixed>
     */
    protected function getScriptData(): array
    {
        return [];
    }

    /**
     * @return array<string>
     */
    protected function getMigrations(): array
    {
        $migrations = [];
        $migrationPath = __DIR__ . '/../database/migrations/';

        if (is_dir($migrationPath)) {
            $files = scandir($migrationPath);

            foreach ($files as $file) {
                if (str_ends_with($file, '.php')) {
                    $migrations[] = pathinfo($file, PATHINFO_FILENAME);
                }
            }
        }

        return $migrations;
    }

    /**
     * @return array<string>
     */
    protected function getConfigs(): array
    {
        $configs = [];
        $configPath =__DIR__ . '/../config/';

        if (is_dir($configPath)) {
            $files = scandir($configPath);

            foreach ($files as $file) {
                if (str_ends_with($file, '.php')) {
                    $configs[] = pathinfo($file, PATHINFO_FILENAME);
                }
            }
        }

        return $configs;
    }

    public function mergeConfigFrom($path, $key): void
    {
        parent::mergeConfigFrom($path, $key);
    }

    public function loadRoutesFrom($path): void
    {
        parent::loadRoutesFrom($path);
    }


}
