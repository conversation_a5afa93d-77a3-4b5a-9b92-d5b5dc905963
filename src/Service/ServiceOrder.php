<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin\Service;

use Exception;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;
use Stephenchenorg\BaseFilamentPlugin\Contracts\OrderServiceInterface;
use Stephenchenorg\BaseFilamentPlugin\DTO\Order\CalculateOrderAmountsDTO;
use Stephenchenorg\BaseFilamentPlugin\DTO\Order\CreateOrderDTO;
use Stephenchenorg\BaseFilamentPlugin\DTO\Order\OrderItemListDTO;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumInvoiceMethod;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumOrderStatus;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumPaymentStatus;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumShippingMethod;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumShippingStatus;
use Stephenchenorg\BaseFilamentPlugin\Models\Client;
use Stephenchenorg\BaseFilamentPlugin\Models\Customer;
use Stephenchenorg\BaseFilamentPlugin\Models\Order;
use Stephenchenorg\BaseFilamentPlugin\Models\OrderItem;
use Stephenchenorg\BaseFilamentPlugin\Models\Product;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductSpecification;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductSpecificationReservingItem;
use Stephenchenorg\BaseFilamentPlugin\Models\ShippingMethod;

/**
 *  訂單服務
 */
class ServiceOrder implements OrderServiceInterface
{

    protected ServiceProductSpecification $serviceProductSpecification;

    public function __construct(ServiceProductSpecification $serviceProductSpecification)
    {
        $this->serviceProductSpecification = $serviceProductSpecification;
    }

    /**
     * 建立訂單
     *
     * @param CreateOrderDTO $data 包含完整的結帳資料
     * @return Order 新建立的訂單物件
     * @throws Exception 如果建立訂單失敗，會拋出異常
     */
    public function createOrder(CreateOrderDTO $data): Order
    {
        DB::beginTransaction();

        try {

            // 檢查庫存並鎖定
            $this->checkInventory($data->items);

            // 檢查登入狀態並取得 orderable 資訊
            $orderableData = $this->getOrderableData();

            // 計算訂單金額
            $calculateDTO = new CalculateOrderAmountsDTO(
                items: $data->items,
                invoice_method: $data->invoice_method,
                shipping_method: $data->shipping_method,
                redeem_points: $data->redeem_points ?? 0
            );
            $orderData = $this->calculateOrderAmounts($calculateDTO);

            // 產生訂單編號
            $orderKey = $this->generateOrderKey();

            // 建立訂單
            $order = Order::create([
                'orderable_type' => $orderableData['type'],
                'orderable_id' => $orderableData['id'],
                'order_key' => $orderKey,
                'total_amount_untaxed' => $orderData['total_amount_untaxed'],
                'total_amount_taxed' => $orderData['total_amount_taxed'],
                'shipping_cost' => $orderData['shipping_cost'],
                'redeem_points' => $orderData['redeem_points'],
                'item_amount' => $orderData['item_amount'],
                'payment_method' => $data->payment_method,
                'payment_status' => EnumPaymentStatus::UNPAID->value,
                'shipping_method' => $data->shipping_method,
                'shipping_status' => EnumShippingStatus::UNSHIPPED->value,
                'status' => EnumOrderStatus::PENDING->value,
                'name' => $data->name,
                'phone' => $data->phone,
                'email' => $data->email,
                'payment_gateway' => config('cs.payment_gateway') ?? null,

                // 地址資訊
                'store_address_id' => $data->store_address_id,
                'country_code' => $data->country_code,
                'state' => $data->state,
                'city' => $data->city,
                'district' => $data->district,
                'postal_code' => $data->postal_code,
                'address_line1' => $data->address_line1,
                'address_line2' => $data->address_line2,

                // 發票資訊
                'invoice_method' => $data->invoice_method,
                'carrier_value' => $data->carrier_value,
                'invoice_address' => $data->invoice_address,
                'vat' => $data->vat,
                'invoice_title' => $data->invoice_title,
                'love_code' => $data->love_code,
            ]);

            // 建立訂單項目
            $this->createOrderItems($order, $data->items);

            DB::commit();
            return $order->load('items');

        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 計算訂單總金額
     *
     * @param CalculateOrderAmountsDTO $data
     * @return array
     * @throws ValidationException
     */
    public function calculateOrderAmounts(CalculateOrderAmountsDTO $data): array
    {
        $itemAmount = $this->getItemCost($data->items);

        $shippingCost = $this->getShippingCost($data->items, $data->shipping_method);

        $redeemCost = $this->getRedeemPointsCost($data->redeem_points);

        $totalAmountUntaxed = $itemAmount + $shippingCost - $redeemCost;

        // 檢查是否為三聯發票且需要加稅
        $invoiceMethodEnum = EnumInvoiceMethod::tryFrom($data->invoice_method);
        $shouldApplyTax = $invoiceMethodEnum &&
            $invoiceMethodEnum->isTriplicate() &&
            config('cs.triplicate_tax', true);

        $totalAmountTaxed = $shouldApplyTax ?
            round($totalAmountUntaxed * 1.05, 2) :
            $totalAmountUntaxed;

        $tax = $totalAmountTaxed - $totalAmountUntaxed;

        return [
            'item_amount' => $itemAmount,
            'shipping_cost' => $shippingCost,
            'redeem_points' => $data->redeem_points,
            'total_amount_untaxed' => $totalAmountUntaxed,
            'total_amount_taxed' => $totalAmountTaxed,
            'tax' => $tax,
        ];
    }

    /**
     * 檢查訂單用到的庫存量是否足夠
     *
     * @param OrderItemListDTO $items
     * @return void
     * @throws ValidationException
     */
    public function checkInventory(OrderItemListDTO $items): void
    {
        $errors = [];

        foreach ($items->items as $index => $item) {
            $productSpecificationId = $item->product_specification_id;
            $requestedQuantity = $item->quantity;

            // 使用 lockForUpdate 鎖定產品規格記錄
            $specification = ProductSpecification::query()
                ->where('id', $productSpecificationId)
                ->lockForUpdate()
                ->first();

            if (!$specification) {
                $errors["items.$index.product_specification_id"] = ['產品規格不存在'];
                continue;
            }

            // 計算已預留的數量
            $reservedQuantity = ProductSpecificationReservingItem::query()
                ->where('product_specification_id', $productSpecificationId)
                ->sum('quantity');

            // 計算可用庫存
            $availableInventory = $specification->inventory - $reservedQuantity;

            // 檢查庫存是否足夠
            if ($availableInventory < $requestedQuantity) {
                $errors["items.$index.product_specification_id"] = ['庫存不足'];
            }
        }

        // 如果有錯誤，拋出 ValidationException
        if (!empty($errors)) {
            throw ValidationException::withMessages($errors);
        }

    }


    /**
     * 獲得訂單可用的物流方式以及對應運費
     *
     * @param OrderItemListDTO $items
     * @return array
     */
    public function getAvailableShippingMethodAndCost(OrderItemListDTO $items): array
    {
        // 取得所有可用的物流方式
        $availableShippingMethodValues = EnumShippingMethod::getAvailableMethodValues();

        $specIds = array_map(function ($item) {
            return $item->product_specification_id;
        }, $items->items);

        $specIdToProductId = ProductSpecification::query()
            ->whereIn('id', $specIds)
            ->get()
            ->pluck('product_id', 'id')
            ->toArray();

        // 各個『產品』的購買數量
        $quantityKeyByProductId = collect($items->items)->reduce(function ($carry, $item) use ($specIdToProductId) {
            $specId = $item->product_specification_id;
            $productId = $specIdToProductId[$specId];
            if (empty($carry[$productId])) $carry[$productId] = 0;
            $carry[$productId] += $item->quantity;
            return $carry;
        }, []);


        // 此次訂單所涉及到的『產品』
        $products = Product::query()
            ->with([
                'shippingMethods'
            ])
            ->whereHas('specifications', function ($query) use ($specIds) {
                $query->whereIn('id', $specIds);
            })
            ->get();

        // 計算每一個物流的金額
        $result = [];
        foreach ($availableShippingMethodValues as $shippingMethod) {

            // 如果沒有該物流方式的資料 直接跳過
            $methodModel = ShippingMethod::query()->where('type', '=', $shippingMethod)->first();
            if (empty($methodModel)) continue;
            $defaultCost = $methodModel->default_amount;

            // 找出哪些產品要『跟不同產品疊加運費』
            $combineProducts = $products->filter(function ($product) use ($shippingMethod) {
                // 先查看是否有『單品設定』
                $productShippingMethod = $product->shippingMethods->where('type', '=', $shippingMethod)->first();

                // 如果沒有『單品設定』那麼就是默認『不疊加』
                if (empty($productShippingMethod)) return false;

                // 如果有『單品設定』則依照他選擇的模式
                return $productShippingMethod->pivot->combine_different_product == 1;
            });

            $combineProductIds = $combineProducts->pluck('id')->toArray();

            // 找出哪些產品不要『跟不同產品疊加運費』
            $doNotCombineProducts = $products->filter(function ($product) use ($combineProductIds) {
                return !in_array($product->id, $combineProductIds);
            });

            // 統計該筆物流方式的運費
            $shippingCost = 0;
            // 統計該筆物流方式是否支援
            $flag = true;

            // 要疊加的先疊加起來
            foreach ($combineProducts as $combineProduct) {

                // 先查看是否有『單品設定』
                $productShippingMethod = $combineProduct->shippingMethods->where('type', '=', $shippingMethod)->first();

                // 若沒有『單品設定』則使用預設的運費
                if (empty($productShippingMethod)) {
                    $shippingCost += $defaultCost;
                    continue;
                };

                // 如果有『單品設定』但是該物流方式關閉 則直接回傳 null
                if ($productShippingMethod->pivot->status == 0) {
                    $flag = false;
                    continue;
                }

                // 如果有『單品設定』但是相同商品不需要疊加
                if ($productShippingMethod->pivot->combine_same_product == 0) {
                    $shippingCost += $productShippingMethod->pivot->amount;
                    continue;
                }

                // 如果有『單品設定』並且相同商品運費要疊加
                $shippingCost += $productShippingMethod->pivot->amount * $quantityKeyByProductId[$combineProduct->id];
            }

            // 不疊加的商品 運費就跟剛剛疊加的總金額 取MAX
            foreach ($doNotCombineProducts as $doNotCombineProduct) {

                // 先查看是否有『單品設定』
                $productShippingMethod = $doNotCombineProduct->shippingMethods->where('type', '=', $shippingMethod)->first();

                // 若沒有『單品設定』則使用預設的運費
                if (empty($productShippingMethod)) {
                    $shippingCost = max($shippingCost, $defaultCost);
                    continue;
                };

                // 如果有『單品設定』但是該物流方式是關閉的 則直接回傳 null
                if ($productShippingMethod->pivot->status == 0) {
                    $flag = false;
                    continue;
                }

                // 如果有『單品設定』但是相同商品不需要疊加
                if ($productShippingMethod->pivot->combine_same_product == 0) {
                    $shippingCost = max($shippingCost, $productShippingMethod->pivot->amount);
                    continue;
                }

                // 如果有『單品設定』並且相同商品運費要疊加
                $shippingCost = max($shippingCost, $productShippingMethod->pivot->amount * $quantityKeyByProductId[$doNotCombineProduct->id]);
            }

            if ($flag) {
                $result[$shippingMethod] = $shippingCost;
            }
        }

        return $result;
    }


    /**
     * 獲得該筆訂單最大可抵免的紅利點數
     *
     * @param OrderItemListDTO $items
     * @return int
     *
     */
    public function getMaxRedeemablePoints(OrderItemListDTO $items): int
    {
        $userPoints = 0;
        if(auth()->guard('customers')->check()){
            $userPoints = auth()->guard('customers')->user()->reward_points;
        }
        if(auth()->guard('clients')->check()){
            $userPoints = auth()->guard('clients')->user()->reward_points;
        }

        $exchangeRate = (float)(config('cs.redeem_point_exchange_rate'));
        $itemAmount = $this->getItemCost($items);
        // 商品金額等同於多少紅利點數
        $itemToPoints = (int)floor($itemAmount / $exchangeRate);


        return min($userPoints, $itemToPoints);
    }

    /**
     * 產生訂單編號
     */
    protected function generateOrderKey(): string
    {
        $date = date('md');
        $unique = uniqid();
        return $date . $unique;
    }

    /**
     * 檢查登入狀態並取得 orderable 資訊
     */
    protected function getOrderableData(): array
    {
        // 檢查 customers guard
        if (Auth::guard('customers')->check()) {
            return [
                'type' => Customer::class,
                'id' => Auth::guard('customers')->id(),
            ];
        }

        // 檢查 clients guard
        if (Auth::guard('clients')->check()) {
            return [
                'type' => Client::class,
                'id' => Auth::guard('clients')->id(),
            ];
        }

        // 未登入狀態，返回 null
        return [
            'type' => null,
            'id' => null,
        ];
    }

    /**
     * 計算運費
     */
    protected function getShippingCost(OrderItemListDTO $items, string $shippingMethod): float
    {
        $availableShippingMethodAndCost = $this->getAvailableShippingMethodAndCost($items);
        return $availableShippingMethodAndCost[$shippingMethod];
    }

    /**
     * 計算商品項目金額
     */
    protected function getItemCost(OrderItemListDTO $items): float
    {
        $itemAmount = 0;
        foreach ($items->items as $item) {
            $specification = ProductSpecification::findOrFail($item->product_specification_id);
            $sellingPrice = $this->serviceProductSpecification->getSellingPrice($specification);
            $quantity = $item->quantity;
            $itemAmount += $sellingPrice * $quantity;
        }
        return $itemAmount;
    }

    protected function getRedeemPointsCost(int $redeemPoints): float
    {
        return $redeemPoints * (float)config('cs.redeem_point_exchange_rate');
    }

    /**
     * 建立訂單項目
     */
    protected function createOrderItems(Order $order, OrderItemListDTO $items): void
    {
        foreach ($items->items as $item) {

            $lang = App::getLocale() ?? ServiceLanguage::getDefaultLanguage();
            $specification = ProductSpecification::query()->findOrFail($item->product_specification_id);
            $translationTitle = $specification->product->translations()->where('lang', $lang)->first()->title;
            $title = $translationTitle;

            if (!empty($specification->combination_key)) {
                $serviceProductSpecification = app(ServiceProductSpecification::class);
                $combinationName = $serviceProductSpecification->getCombinationName($specification->combination_key, $lang);
                if ($combinationName) {
                    $title .= ' (' . $combinationName . ')';
                }
            }

            $sellingPrice = $this->serviceProductSpecification->getSellingPrice($specification);
            $quantity = $item->quantity;


            OrderItem::query()->create([
                'order_id' => $order->id,
                'product_specification_id' => $item->product_specification_id,
                'title' => $title,
                'quantity' => $item->quantity,
                'unit_price' => $sellingPrice,
                'total_amount' => $sellingPrice * $quantity,
            ]);

            // 建立預留記錄
            $productSpecificationId = $item->product_specification_id;
            $quantity = $item->quantity;

            // 查找現有的預留記錄
            $reservingItem = ProductSpecificationReservingItem::query()
                ->where('product_specification_id', $productSpecificationId)
                ->first();

            if ($reservingItem) {
                // 如果已存在，增加預留量
                $reservingItem->increment('quantity', $quantity);
            } else {
                // 如果不存在，建立新記錄
                ProductSpecificationReservingItem::query()->create([
                    'product_specification_id' => $productSpecificationId,
                    'quantity' => $quantity
                ]);
            }

        }
    }


}
