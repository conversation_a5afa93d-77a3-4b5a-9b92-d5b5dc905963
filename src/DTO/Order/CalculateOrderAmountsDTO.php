<?php

namespace Stephenchenorg\BaseFilamentPlugin\DTO\Order;

/**
 * 計算訂單金額 DTO
 */
class CalculateOrderAmountsDTO
{
    /**
     * @param OrderItemDTO[] $items
     */
    public function __construct(
        public readonly array $items,
        public readonly string $shipping_method,
        public readonly string $invoice_method,
        public readonly int $redeem_points = 0
    ) {}

    /**
     * 從陣列創建 DTO
     */
    public static function fromArray(array $items, string $shipping_method, string $invoice_method, int $redeem_points = 0): self
    {
        return new self(
            items: OrderItemDTO::fromArrayCollection($items),
            shipping_method: $shipping_method,
            invoice_method: $invoice_method,
            redeem_points: $redeem_points
        );
    }

    /**
     * 取得項目陣列格式（用於向後相容）
     */
    public function getItemsArray(): array
    {
        return OrderItemDTO::toArrayCollection($this->items);
    }
}
