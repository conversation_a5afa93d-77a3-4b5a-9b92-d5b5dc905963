<?php

namespace Stephenchenorg\BaseFilamentPlugin\DTO\Order;

/**
 * 計算訂單金額 DTO
 */
class CalculateOrderAmountsDTO
{
    /**
     * @param OrderItemDTO[] $items
     */
    public function __construct(
        public readonly array $items,
        public readonly string $invoice_method,
        public readonly string $shipping_method,
        public readonly int $redeem_points = 0
    ) {}

    /**
     * 從陣列創建 DTO
     */
    public static function fromArray(array $data): self
    {
        return new self(
            items: OrderItemDTO::fromArrayCollection($data['items']),
            invoice_method: $data['invoice_method'],
            shipping_method: $data['shipping_method'],
            redeem_points: $data['redeem_points'] ?? 0
        );
    }

    /**
     * 轉換為陣列
     */
    public function toArray(): array
    {
        return [
            'items' => OrderItemDTO::toArrayCollection($this->items),
            'invoice_method' => $this->invoice_method,
            'shipping_method' => $this->shipping_method,
            'redeem_points' => $this->redeem_points,
        ];
    }

    /**
     * 從陣列集合創建 DTO 集合
     *
     * @param array $dataCollection
     * @return CalculateOrderAmountsDTO[]
     */
    public static function fromArrayCollection(array $dataCollection): array
    {
        return array_map(fn($data) => self::fromArray($data), $dataCollection);
    }

    /**
     * 將 DTO 集合轉換為陣列集合
     *
     * @param CalculateOrderAmountsDTO[] $dtos
     * @return array
     */
    public static function toArrayCollection(array $dtos): array
    {
        return array_map(fn($dto) => $dto->toArray(), $dtos);
    }
}
