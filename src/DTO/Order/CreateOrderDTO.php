<?php

namespace Stephenchenorg\BaseFilamentPlugin\DTO\Order;

/**
 * 建立訂單 DTO
 */
class CreateOrderDTO
{
    /**
     * @param OrderItemDTO[] $items
     */
    public function __construct(
        // 購物車項目
        public readonly array $items,
        
        // 點數折抵
        public readonly int $redeem_points = 0,
        
        // 基本資訊
        public readonly string $name,
        public readonly string $phone,
        public readonly string $email,
        
        // 物流資訊
        public readonly string $shipping_method,
        public readonly ?string $shipping_name = null,
        public readonly ?string $shipping_phone = null,
        public readonly ?string $shipping_address = null,
        public readonly ?string $shipping_store_id = null,
        public readonly ?string $shipping_store_name = null,
        public readonly ?string $shipping_store_address = null,
        
        // 發票資訊
        public readonly string $invoice_method,
        public readonly ?string $carrier_type = null,
        public readonly ?string $carrier_num = null,
        public readonly ?string $vat = null,
        public readonly ?string $invoice_title = null,
        public readonly ?string $love_code = null,
        
        // 備註
        public readonly ?string $note = null
    ) {}

    /**
     * 從陣列創建 DTO
     */
    public static function fromArray(array $data): self
    {
        return new self(
            items: OrderItemDTO::fromArrayCollection($data['items']),
            redeem_points: $data['redeem_points'] ?? 0,
            name: $data['name'],
            phone: $data['phone'],
            email: $data['email'],
            shipping_method: $data['shipping_method'],
            shipping_name: $data['shipping_name'] ?? null,
            shipping_phone: $data['shipping_phone'] ?? null,
            shipping_address: $data['shipping_address'] ?? null,
            shipping_store_id: $data['shipping_store_id'] ?? null,
            shipping_store_name: $data['shipping_store_name'] ?? null,
            shipping_store_address: $data['shipping_store_address'] ?? null,
            invoice_method: $data['invoice_method'],
            carrier_type: $data['carrier_type'] ?? null,
            carrier_num: $data['carrier_num'] ?? null,
            vat: $data['vat'] ?? null,
            invoice_title: $data['invoice_title'] ?? null,
            love_code: $data['love_code'] ?? null,
            note: $data['note'] ?? null
        );
    }

    /**
     * 轉換為陣列
     */
    public function toArray(): array
    {
        return [
            'items' => OrderItemDTO::toArrayCollection($this->items),
            'redeem_points' => $this->redeem_points,
            'name' => $this->name,
            'phone' => $this->phone,
            'email' => $this->email,
            'shipping_method' => $this->shipping_method,
            'shipping_name' => $this->shipping_name,
            'shipping_phone' => $this->shipping_phone,
            'shipping_address' => $this->shipping_address,
            'shipping_store_id' => $this->shipping_store_id,
            'shipping_store_name' => $this->shipping_store_name,
            'shipping_store_address' => $this->shipping_store_address,
            'invoice_method' => $this->invoice_method,
            'carrier_type' => $this->carrier_type,
            'carrier_num' => $this->carrier_num,
            'vat' => $this->vat,
            'invoice_title' => $this->invoice_title,
            'love_code' => $this->love_code,
            'note' => $this->note,
        ];
    }

    /**
     * 取得項目陣列格式（用於向後相容）
     */
    public function getItemsArray(): array
    {
        return OrderItemDTO::toArrayCollection($this->items);
    }
}
