<?php

namespace Stephenchenorg\BaseFilamentPlugin\DTO\Order;

/**
 * 建立訂單 DTO
 */
class CreateOrderDTO
{
    /**
     * @param OrderItemDTO[] $items
     */
    public function __construct(
        // 購物車項目
        public readonly array $items,

        // 點數折抵
        public readonly ?int $redeem_points,

        // 基本資訊
        public readonly string $name,
        public readonly string $phone,
        public readonly string $email,

        // 付款方式
        public readonly string $payment_method,

        // 運送方式
        public readonly string $shipping_method,

        // 地址資訊
        public readonly ?int $store_address_id,
        public readonly ?string $country_code,
        public readonly ?string $state,
        public readonly ?string $city,
        public readonly ?string $district,
        public readonly ?string $postal_code,
        public readonly ?string $address_line1,
        public readonly ?string $address_line2,

        // 發票資訊
        public readonly string $invoice_method,
        public readonly ?string $carrier_value,
        public readonly ?string $invoice_address,
        public readonly ?string $vat,
        public readonly ?string $invoice_title,
        public readonly ?string $love_code
    ) {}

    /**
     * 從陣列創建 DTO
     */
    public static function fromArray(array $data): self
    {
        return new self(
            items: OrderItemDTO::fromArrayCollection($data['items']),
            redeem_points: $data['redeem_points'] ?? null,
            name: $data['name'],
            phone: $data['phone'],
            email: $data['email'],
            payment_method: $data['payment_method'],
            shipping_method: $data['shipping_method'],
            store_address_id: $data['store_address_id'] ?? null,
            country_code: $data['country_code'] ?? null,
            state: $data['state'] ?? null,
            city: $data['city'] ?? null,
            district: $data['district'] ?? null,
            postal_code: $data['postal_code'] ?? null,
            address_line1: $data['address_line1'] ?? null,
            address_line2: $data['address_line2'] ?? null,
            invoice_method: $data['invoice_method'],
            carrier_value: $data['carrier_value'] ?? null,
            invoice_address: $data['invoice_address'] ?? null,
            vat: $data['vat'] ?? null,
            invoice_title: $data['invoice_title'] ?? null,
            love_code: $data['love_code'] ?? null
        );
    }

    /**
     * 轉換為陣列
     */
    public function toArray(): array
    {
        return [
            'items' => OrderItemDTO::toArrayCollection($this->items),
            'redeem_points' => $this->redeem_points,
            'name' => $this->name,
            'phone' => $this->phone,
            'email' => $this->email,
            'payment_method' => $this->payment_method,
            'shipping_method' => $this->shipping_method,
            'store_address_id' => $this->store_address_id,
            'country_code' => $this->country_code,
            'state' => $this->state,
            'city' => $this->city,
            'district' => $this->district,
            'postal_code' => $this->postal_code,
            'address_line1' => $this->address_line1,
            'address_line2' => $this->address_line2,
            'invoice_method' => $this->invoice_method,
            'carrier_value' => $this->carrier_value,
            'invoice_address' => $this->invoice_address,
            'vat' => $this->vat,
            'invoice_title' => $this->invoice_title,
            'love_code' => $this->love_code,
        ];
    }

    /**
     * 從陣列集合創建 DTO 集合
     *
     * @param array $dataCollection
     * @return CreateOrderDTO[]
     */
    public static function fromArrayCollection(array $dataCollection): array
    {
        return array_map(fn($data) => self::fromArray($data), $dataCollection);
    }

    /**
     * 將 DTO 集合轉換為陣列集合
     *
     * @param CreateOrderDTO[] $dtos
     * @return array
     */
    public static function toArrayCollection(array $dtos): array
    {
        return array_map(fn($dto) => $dto->toArray(), $dtos);
    }
}
